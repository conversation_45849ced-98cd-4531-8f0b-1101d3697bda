import { cn } from '@/lib/utils';
import { Badge } from '../Badge';

interface TableHeaderCountProps {
  title: string;
  total: number;
  className?: string;
}

const TableHeaderCount = ({ title, total, className }: TableHeaderCountProps) => {
  return (
    <div className={cn('px-4 py-6', className)}>
      <div className='flex items-center gap-3'>
        <h3 className='text-lg font-medium text-gray-900'>{title}</h3>
        <Badge className='text-xs font-medium'>{total}</Badge>
      </div>
    </div>
  );
};

export default TableHeaderCount;
