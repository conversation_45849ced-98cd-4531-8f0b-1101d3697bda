import { AdminNotification } from '@/generated/graphql';
import useNotificationActions from './hooks/useNotificationActions';

export interface NotificationItemProps {
  item: AdminNotification;
  actions: ReturnType<typeof useNotificationActions>;
  isAdmin?: boolean;
}

export interface NotificationDataComputations {
  isRead: boolean;
  isPostReport: boolean;
  isClubCreationRequest: boolean;
  isClubCreationRequestDeclined: boolean;
  isClubCreationRequestApproved: boolean;
  isClubCreationRequestHasAction: boolean;
  hasOpenReports: boolean;
  displayName: string;
  avatarUrl: string | undefined;
  shortName: string;
  categoryTitle: string;
  clubRequestName: string;
  description: string;
  unflagLabel: string;
  deleteLabel: string;
  hasUnflagAction: boolean;
  hasDeleteAction: boolean;
}

export interface NotificationUserInfoProps {
  displayName: string;
  avatarUrl: string | undefined;
  shortName: string;
}

export interface NotificationStatusBadgeProps {
  hasOpenReports: boolean;
  categoryTitle: string;
  isClubCreationRequestHasAction: boolean;
  isClubCreationRequestApproved: boolean;
  isClubCreationRequestDeclined: boolean;
}

export interface NotificationHeaderProps {
  isClubCreationRequest: boolean;
  isPostReport: boolean;
  reportedAt: string;
  isRead: boolean;
}

export interface NotificationContentProps {
  isClubCreationRequest: boolean;
  clubRequestName: string;
  description: string;
}

export interface NotificationActionsProps {
  isClubCreationRequest: boolean;
  isAdmin?: boolean;
  hasUnflagAction: boolean;
  hasDeleteAction: boolean;
  unflagLabel: string;
  deleteLabel: string;
  isClubCreationRequestHasAction: boolean;
  isDecliningClubRequest: boolean;
  isUnflagging: boolean;
  isRemoving: boolean;
  onApproveRequest: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onDeclineRequest: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onUnflagItem: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onRemoveItem: (e: React.MouseEvent<HTMLButtonElement>) => void;
}
