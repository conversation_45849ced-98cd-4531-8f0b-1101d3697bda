import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { NotificationActionsProps } from '../types';

export function NotificationActions({
  isClubCreationRequest,
  isAdmin,
  hasUnflagAction,
  hasDeleteAction,
  unflagLabel,
  deleteLabel,
  isClubCreationRequestHasAction,
  isDecliningClubRequest,
  isUnflagging,
  isRemoving,
  onApproveRequest,
  onDeclineRequest,
  onUnflagItem,
  onRemoveItem,
}: NotificationActionsProps) {
  if (isClubCreationRequest) {
    return (
      <div className='w-full flex justify-between gap-2 sm:gap-3'>
        <Button
          variant={'outline'}
          className='flex-1 z-10 text-gray-700 text-xs sm:text-sm h-8 sm:h-10 '
          onClick={onDeclineRequest}
          disabled={isDecliningClubRequest || !isAdmin || isClubCreationRequestHasAction}
        >
          Decline
        </Button>
        <Button
          variant={'default'}
          className='flex-1 text-white text-xs sm:text-sm h-8 sm:h-10 '
          onClick={onApproveRequest}
          disabled={!isAdmin || isClubCreationRequestHasAction || isDecliningClubRequest}
        >
          Approve
        </Button>
      </div>
    );
  }

  return (
    <div className='w-full flex justify-between gap-2 sm:gap-3'>
      <Button
        variant={'outline'}
        className={cn(
          'flex-1 z-10 text-gray-700 text-xs sm:text-sm h-8 sm:h-10',
          hasUnflagAction && 'cursor-not-allowed'
        )}
        onClick={onUnflagItem}
        disabled={hasUnflagAction || isUnflagging || !isAdmin}
      >
        {unflagLabel}
      </Button>
      <Button
        variant={'destructive'}
        className={cn(
          'flex-1 text-white text-xs sm:text-sm h-8 sm:h-10',
          hasDeleteAction && 'cursor-not-allowed'
        )}
        onClick={onRemoveItem}
        disabled={hasDeleteAction || isRemoving || !isAdmin}
      >
        {deleteLabel}
      </Button>
    </div>
  );
}
