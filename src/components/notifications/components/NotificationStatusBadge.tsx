import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import { Flag } from 'lucide-react';
import { NotificationStatusBadgeProps } from '../types';

export function NotificationStatusBadge({
  hasOpenReports,
  categoryTitle,
  isClubCreationRequestHasAction,
  isClubCreationRequestApproved,
  isClubCreationRequestDeclined,
}: NotificationStatusBadgeProps) {
  if (!hasOpenReports && !isClubCreationRequestHasAction) {
    return null;
  }

  if (hasOpenReports) {
    return (
      <Badge className='bg-[#FCEFED] px-1.5 py-1 hover:bg-[#FCEFED] gap-1 font-medium text-xs text-destructive-foreground border-none whitespace-nowrap flex-shrink-0 sm:px-2 sm:text-sm'>
        <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
        <span className='truncate max-w-[80px] sm:max-w-none'>{categoryTitle}</span>
      </Badge>
    );
  }

  if (isClubCreationRequestHasAction) {
    return (
      <Badge
        className={cn(
          'bg-[#FCEFED] px-1.5 py-1 hover:bg-[#FCEFED] gap-1 font-medium text-xs text-destructive-foreground border-none whitespace-nowrap flex-shrink-0 sm:px-2 sm:text-sm',
          isClubCreationRequestApproved && 'bg-[#ECFDF3] hover:bg-[#ECFDF3] text-[#027A48]'
        )}
      >
        <Flag
          className={cn(
            'w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground',
            isClubCreationRequestApproved && 'bg-[#ECFDF3] hover:bg-[#ECFDF3] text-[#027A48]'
          )}
        />
        <span className='truncate max-w-[80px] sm:max-w-none'>
          {isClubCreationRequestDeclined ? 'Declined' : 'Approved'}
        </span>
      </Badge>
    );
  }

  return null;
}
