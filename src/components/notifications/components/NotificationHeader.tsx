import { formatNotificationTime } from '@/lib/utils';
import { NotificationHeaderProps } from '../types';

export function NotificationHeader({
  isClubCreationRequest,
  isPostReport,
  reportedAt,
  isRead,
}: NotificationHeaderProps) {
  const getNotificationTypeLabel = () => {
    if (isClubCreationRequest) return 'Club Creation Request';
    if (isPostReport) return 'Flagged Post';
    return 'Flagged Event';
  };

  return (
    <div className='flex justify-between items-start'>
      <div className='flex flex-col gap-1 sm:flex-row sm:gap-4 sm:items-center'>
        <p className='text-sm font-medium text-gray-900'>{getNotificationTypeLabel()}</p>
        <p className='text-[13px] leading-5 text-gray-700'>{formatNotificationTime(reportedAt)}</p>
      </div>
      {!isRead && <div className='bg-destructive w-2 h-2 rounded-full flex-shrink-0 mt-1' />}
    </div>
  );
}
