import { useMemo } from 'react';
import {
  AdminNotification,
  AdminNotificationType,
  AdminReportActionType,
  ReportStatusEnum,
} from '@/generated/graphql';
import { getDisplayName } from '@/pages/club-detail/utils';
import { NotificationDataComputations } from '../types';

export function useNotificationDataComputations(
  item: AdminNotification
): NotificationDataComputations {
  return useMemo(() => {
    const isRead = item.isRead;
    const isPostReport = item.type === AdminNotificationType.ClubPostReport;
    const isClubCreationRequest = item.type === AdminNotificationType.ClubCreationRequest;

    const isClubCreationRequestDeclined =
      isClubCreationRequest && item.action === AdminReportActionType.Declined;
    const isClubCreationRequestApproved =
      isClubCreationRequest && item.action === AdminReportActionType.Approved;
    const isClubCreationRequestHasAction =
      isClubCreationRequestDeclined || isClubCreationRequestApproved;

    const hasOpenReports = isPostReport
      ? (item.clubPost?.reports?.some((report) => report.status === ReportStatusEnum.Open) ?? false)
      : (item.clubEvent?.reports?.some((report) => report.status === ReportStatusEnum.Open) ??
        false);

    const clubProfile = item.clubPost?.clubProfile;
    const displayName = isClubCreationRequest
      ? getDisplayName(item.clubRequest?.user?.firstName, item.clubRequest?.user?.lastName)
      : isPostReport
        ? getDisplayName(clubProfile?.user?.firstName, clubProfile?.user?.lastName)
        : getDisplayName(
            item.clubEvent?.clubProfile?.user?.firstName,
            item.clubEvent?.clubProfile?.user?.lastName
          );

    const avatarUrl =
      (isClubCreationRequest
        ? item.clubRequest?.clubProfile?.img?.url
        : isPostReport
          ? clubProfile?.img?.url
          : item.clubEvent?.clubProfile?.img?.url) || undefined;

    const shortName = displayName
      .split(' ')
      .map((s) => s[0])
      .filter(Boolean)
      .slice(0, 2)
      .join('')
      .toUpperCase();

    const categoryTitle = (() => {
      if (isClubCreationRequest) {
        return '';
      }

      const reports = isPostReport
        ? item.clubPost?.reports?.filter((rp) => rp.status === ReportStatusEnum.Open)
        : item.clubEvent?.reports?.filter((rp) => rp.status === ReportStatusEnum.Open);

      if (!reports || reports.length === 0) return 'Other';

      // Get the latest report (most recent createdAt)
      const latestReport = reports.reduce((latest, current) => {
        return new Date(current.createdAt) > new Date(latest.createdAt) ? current : latest;
      });

      return latestReport.category?.title ?? 'Other';
    })();

    const clubRequestName = isClubCreationRequest ? (item.clubRequest?.clubName ?? '') : '';

    const description = isClubCreationRequest
      ? (item.clubRequest?.clubDescription ?? '')
      : isPostReport
        ? (item.clubPost?.content ?? '')
        : (item.clubEvent?.description ?? '');

    const unflagLabel = isPostReport ? 'Unflag Post' : 'Unflag Event';
    const deleteLabel = isPostReport ? 'Delete Post' : 'Delete Event';
    const hasUnflagAction = item.action === AdminReportActionType.Unflag;
    const hasDeleteAction = item.action === AdminReportActionType.Delete;

    return {
      isRead,
      isPostReport,
      isClubCreationRequest,
      isClubCreationRequestDeclined,
      isClubCreationRequestApproved,
      isClubCreationRequestHasAction,
      hasOpenReports,
      displayName,
      avatarUrl,
      shortName,
      categoryTitle,
      clubRequestName,
      description,
      unflagLabel,
      deleteLabel,
      hasUnflagAction,
      hasDeleteAction,
    };
  }, [item]);
}
