import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/Skeleton';
import { memo, useCallback } from 'react';
import { useNotificationDataComputations } from './hooks/useNotificationDataComputations';
import { NotificationUserInfo } from './components/NotificationUserInfo';
import { NotificationStatusBadge } from './components/NotificationStatusBadge';
import { NotificationHeader } from './components/NotificationHeader';
import { NotificationContent } from './components/NotificationContent';
import { NotificationActions } from './components/NotificationActions';
import { NotificationItemProps } from './types';

const NotificationItem = ({ item, actions, isAdmin }: NotificationItemProps) => {
  const {
    handleUnflagNotification,
    handleRemoveNotification,
    handleMarkAsRead,
    handleNavigate,
    handleApproveClubRequest,
    handleDeclineClubRequest,
    setIsOpen,
    getItemLoadingState,
  } = actions;

  // Get loading states for this specific item
  const itemLoadingState = getItemLoadingState(item.id);
  const { isUnflagging, isRemoving, isDecliningClubRequest } = itemLoadingState;

  const computedData = useNotificationDataComputations(item);
  const {
    isRead,
    isPostReport,
    isClubCreationRequest,
    isClubCreationRequestHasAction,
    hasOpenReports,
    displayName,
    avatarUrl,
    shortName,
    categoryTitle,
    clubRequestName,
    description,
    unflagLabel,
    deleteLabel,
    hasUnflagAction,
    hasDeleteAction,
    isClubCreationRequestApproved,
    isClubCreationRequestDeclined,
  } = computedData;

  const handleUnflagItem = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (isPostReport) {
        handleUnflagNotification(isPostReport, item.clubPost?.id ?? '', item.id);
      } else {
        handleUnflagNotification(isPostReport, item.clubEvent?.id ?? '', item.id);
      }
    },
    [isPostReport, item.clubPost?.id, item.clubEvent?.id, item.id, handleUnflagNotification]
  );

  const handleRemoveItem = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (isPostReport) {
        handleRemoveNotification(isPostReport, item.clubPost?.id ?? '', item.id);
      } else {
        handleRemoveNotification(isPostReport, item.clubEvent?.id ?? '', item.id);
      }
    },
    [isPostReport, handleRemoveNotification, item.clubPost?.id, item.clubEvent?.id, item.id]
  );

  const handleApproveRequest = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (item.clubRequest) {
        handleApproveClubRequest(item.clubRequest);
      }
    },
    [item.clubRequest, handleApproveClubRequest]
  );

  const handleDeclineRequest = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      if (item.clubRequest?.id) {
        handleDeclineClubRequest(item.clubRequest.id, item.id);
      }
    },
    [item.clubRequest?.id, item.id, handleDeclineClubRequest]
  );

  const handleItemClick = useCallback(() => {
    if (!isRead && isAdmin) {
      handleMarkAsRead({ notificationId: item.id });
    }

    if (isClubCreationRequest) {
      handleNavigate({
        isPostReport: false,
        isClubCreationRequest: true,
      });
    } else {
      const associationId = isPostReport
        ? (item.clubPost?.clubProfile?.user?.association?.id ?? '')
        : (item.clubEvent?.clubProfile?.user?.association?.id ?? '');
      const clubId = isPostReport ? (item.clubPost?.clubId ?? '') : (item.clubEvent?.clubId ?? '');
      if (associationId && clubId) {
        handleNavigate({
          isPostReport,
          associationId,
          clubId,
        });
      }
    }

    setIsOpen(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRead, handleNavigate, isPostReport, isClubCreationRequest, item.id, handleMarkAsRead]);

  return (
    <div
      onClick={handleItemClick}
      className={cn(
        'w-full p-4 gap-3 flex flex-col bg-muted sm:p-6 sm:gap-4 cursor-pointer',
        !isRead && 'bg-[#FCEFED80] border-b-[1px]'
      )}
    >
      <NotificationHeader
        isClubCreationRequest={isClubCreationRequest}
        isPostReport={isPostReport}
        reportedAt={item.reportedAt}
        isRead={isRead}
      />

      <div className='flex items-center justify-between gap-2 min-w-0 flex-1'>
        <NotificationUserInfo
          displayName={displayName}
          avatarUrl={avatarUrl}
          shortName={shortName}
        />
        <NotificationStatusBadge
          hasOpenReports={hasOpenReports}
          categoryTitle={categoryTitle}
          isClubCreationRequestHasAction={isClubCreationRequestHasAction}
          isClubCreationRequestApproved={isClubCreationRequestApproved}
          isClubCreationRequestDeclined={isClubCreationRequestDeclined}
        />
      </div>

      <NotificationContent
        isClubCreationRequest={isClubCreationRequest}
        clubRequestName={clubRequestName}
        description={description}
      />

      <NotificationActions
        isClubCreationRequest={isClubCreationRequest}
        isAdmin={isAdmin}
        hasUnflagAction={hasUnflagAction}
        hasDeleteAction={hasDeleteAction}
        unflagLabel={unflagLabel}
        deleteLabel={deleteLabel}
        isClubCreationRequestHasAction={isClubCreationRequestHasAction}
        isDecliningClubRequest={isDecliningClubRequest}
        isUnflagging={isUnflagging}
        isRemoving={isRemoving}
        onApproveRequest={handleApproveRequest}
        onDeclineRequest={handleDeclineRequest}
        onUnflagItem={handleUnflagItem}
        onRemoveItem={handleRemoveItem}
      />
    </div>
  );
};

export default memo(NotificationItem);

export const PostNotificationItemSkeleton = () => {
  return (
    <div className='w-full p-4 gap-3 flex flex-col sm:p-6 sm:gap-4'>
      <div className='flex justify-between items-start'>
        <div className='flex flex-col gap-1 sm:flex-row sm:gap-4'>
          <Skeleton className='h-4 w-28' />
          <Skeleton className='h-4 w-16' />
        </div>
        <Skeleton className='h-2 w-2 rounded-full' />
      </div>

      <div className='flex items-center justify-between gap-2 min-w-0 flex-1'>
        <div className='flex items-center gap-2 sm:gap-3'>
          <Skeleton className='w-8 h-8 sm:w-10 sm:h-10 rounded-full' />
          <Skeleton className='h-4 w-32' />
        </div>
        <Skeleton className='h-6 w-20 rounded-full' />
      </div>

      <Skeleton className='h-4 w-full' />
      <Skeleton className='h-4 w-4/5' />

      <div className='w-full flex justify-between gap-2 sm:gap-3'>
        <Skeleton className='h-8 w-full sm:h-10' />
        <Skeleton className='h-8 w-full sm:h-10' />
      </div>
    </div>
  );
};
