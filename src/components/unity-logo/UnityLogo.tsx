import unityLogo from '@/assets/images/logos/unity-logo.svg';
import unityLogoPng from '@/assets/images/logos/unity-logo-png.png';
import { AppRoutePaths } from '@/lib/constants';
import { Link } from 'react-router-dom';

const UnityLogo = () => {
  return (
    <Link to={AppRoutePaths.USERS}>
      <img src={unityLogo} alt='Unity' className='cursor-pointer' />
      <img src={unityLogoPng} alt='Unity' className='cursor-pointer hidden' />
    </Link>
  );
};

export default UnityLogo;
