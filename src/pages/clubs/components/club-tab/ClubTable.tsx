import TableData from '@/components/ui/table/TableData';
import { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { generateClubColumns } from './ClubColumns';
import { RowSelectionState, Row } from '@tanstack/react-table';
import usePagination from '@/hooks/usePagination';
import ClubTableFilter from './ClubTableFilter';
import useSort from '@/hooks/useSort';
import {
  ClubCategoryEnum,
  OrderDirection,
  ClubOrderByEnum,
  useClubTemplatesQuery,
  ClubTemplate,
  UserRole,
} from '@/generated/graphql';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { DateRange } from 'react-day-picker';
import { useNavigate } from 'react-router-dom';
import { AppRoutePaths, DATE_FORMAT_API } from '@/lib/constants';
import { useClubActions } from '../../hooks/useClubActions';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import { DeleteClubModal } from './DeleteClubModal';
import { format } from 'date-fns';

const ClubTable = () => {
  const { userData, isLoading: isLoadingAuth } = useAuthContext();
  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort({
    initialSort: { id: 'lastUpdated', desc: true },
  });
  const { searchTemp, setSearchTemp } = useSearchQuery('clubs');

  const navigate = useNavigate();

  const clubActions = useClubActions();
  const { selectedClub, handleOpenDeleteClubModal } = clubActions;

  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const actionCellRef = useRef<HTMLDivElement>(null);

  const [selectedCategory, setSelectedCategory] = useState<ClubCategoryEnum | null>(null);
  const [lastUpdated, setLastUpdated] = useState<DateRange | undefined>(undefined);

  // Reset pagination when search or filters change
  useEffect(() => {
    setPagination({ pageIndex: 1, pageSize: 10 });
  }, [searchTemp, selectedCategory, lastUpdated, setPagination]);

  const { data: clubs, loading: isLoadingClubs } = useClubTemplatesQuery({
    variables: {
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp.trim() || undefined,
        category: selectedCategory !== null ? selectedCategory : undefined,
        dateUpdatedFrom: lastUpdated?.from ? format(lastUpdated?.from, DATE_FORMAT_API) : undefined,
        dateUpdatedTo: lastUpdated?.to ? format(lastUpdated?.to, DATE_FORMAT_API) : undefined,
      },
      orderBy:
        sorting.length > 0
          ? {
              field: (() => {
                // Map frontend sorting IDs to API enum values
                switch (sorting[0]?.id) {
                  case 'name':
                    return ClubOrderByEnum.Name;
                  case 'lastUpdated':
                    return ClubOrderByEnum.UpdatedAt; // Try UPDATED_AT again
                  case 'activatedDate':
                    return ClubOrderByEnum.ActivatedDate;
                  default:
                    return ClubOrderByEnum.Name;
                }
              })(),
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
    defaultOptions: {
      fetchPolicy: 'cache-and-network',
    },
  });

  const clubsData = useMemo(() => {
    if (isLoadingClubs || isLoadingAuth) {
      return Array(10).fill({});
    }
    return clubs?.clubTemplates?.items ?? [];
  }, [isLoadingClubs, isLoadingAuth, clubs]);

  const totalClubs = useMemo(() => clubs?.clubTemplates?.total ?? 0, [clubs]);

  const handleRowClick = useCallback(
    (row: Row<ClubTemplate>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        const actionCell = actionCellRef.current;
        const rect = actionCell.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const actionCellRight = rect.right;

        // Only scroll if the actions don't have enough space to display
        if (actionCellRight > viewportWidth) {
          actionCell.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          });
        }
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateClubColumns({
        isLoading: isLoadingClubs,
        onEdit: (club) => navigate(`${AppRoutePaths.CLUBS}/${club.id}`),
        onDelete: handleOpenDeleteClubModal,
        onNavigate: (clubId: string) => navigate(`${AppRoutePaths.CLUBS}/${clubId}`),
        showActions: userData?.role === UserRole.Admin,
        selectedRowId,
        setSelectedRowId,
        actionCellRef,
      }),
    [isLoadingClubs, handleOpenDeleteClubModal, userData?.role, selectedRowId, navigate]
  );

  return (
    <div className='w-full flex flex-col sm:p-8 sm:pb-12 flex-1 rounded-lg'>
      <ClubTableFilter
        search={searchTemp}
        setSearch={setSearchTemp}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        lastUpdated={lastUpdated}
        setLastUpdated={setLastUpdated}
      />
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Clubs' total={totalClubs} />
        <TableData
          columns={columns}
          data={clubsData}
          pagination={pagination}
          sorting={sorting}
          filters={[]}
          onColumnFiltersChange={() => {}}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        {totalClubs > 0 && (
          <TablePagination
            pageCount={Math.ceil((clubs?.clubTemplates?.total || 0) / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        )}
      </div>
      <DeleteClubModal clubActions={clubActions} selectedClub={selectedClub} />
    </div>
  );
};

export default ClubTable;
