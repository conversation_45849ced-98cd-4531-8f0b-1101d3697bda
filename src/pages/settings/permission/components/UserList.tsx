import { User, UserRole } from '@/generated/graphql';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import pluralize from 'pluralize';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { UserActionCell } from './UserActionCell';
import { useCallback, useRef, useState } from 'react';

interface UserListProps {
  users: User[];
  isLoading: boolean;
  role: UserRole;
  showActions: (user: User) => boolean;
  onRemoveUser: (userId: string) => void;
  onEditUser: (user: User) => void;
}

const UserList = ({
  users,
  isLoading,
  role,
  showActions,
  onRemoveUser,
  onEditUser,
}: UserListProps) => {
  const actionCellRef = useRef<HTMLDivElement>(null);
  const [selectedUserId, setSelectedRowId] = useState<string | null>(null);

  const handleRowClick = useCallback(
    (user: User) => {
      const newSelectedUserId = selectedUserId === user.id ? null : user.id;
      setSelectedRowId(newSelectedUserId);

      if (newSelectedUserId && actionCellRef.current) {
        const actionCell = actionCellRef.current;
        const rect = actionCell.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const actionCellRight = rect.right;

        // Only scroll if the actions don't have enough space to display
        if (actionCellRight > viewportWidth) {
          actionCell.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          });
        }
      }
    },
    [selectedUserId]
  );

  return (
    <div className='rounded-md border bg-background'>
      <h2 className='text-lg m-4 mr-0 font-medium text-gray-900'>
        {pluralize(role === UserRole.Admin ? 'Admin' : 'User', users.length)}
      </h2>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-[300px] min-w-20 truncate text-xs text-gray-600'>
              First Name
            </TableHead>
            <TableHead className='w-[300px] min-w-20 truncate text-xs text-gray-600'>
              Last Name
            </TableHead>
            <TableHead className='w-[300px] min-w-20 truncate text-xs text-gray-600'>
              Email
            </TableHead>
            <TableHead className='w-[50px]'></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id} onClick={() => handleRowClick(user)}>
              <TableCell>
                <SkeletonCell
                  className='sm:max-w-[300px] w-[120px] truncate text-sm font-medium text-gray-900'
                  isLoading={isLoading}
                >
                  {user.firstName}
                </SkeletonCell>
              </TableCell>
              <TableCell>
                <SkeletonCell
                  className='sm:max-w-[300px] w-[120px] truncate text-sm font-medium text-gray-900'
                  isLoading={isLoading}
                >
                  {user.lastName}
                </SkeletonCell>
              </TableCell>
              <TableCell>
                <SkeletonCell isLoading={isLoading}>{user.email}</SkeletonCell>
              </TableCell>
              <TableCell>
                <SkeletonCell isLoading={isLoading}>
                  {showActions(user) && (
                    <UserActionCell
                      ref={actionCellRef}
                      onEditUser={() => onEditUser(user)}
                      onRemoveUser={() => onRemoveUser(user.id)}
                      isRowSelected={selectedUserId === user.id}
                      setSelectedRowId={setSelectedRowId}
                    />
                  )}
                </SkeletonCell>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default UserList;
