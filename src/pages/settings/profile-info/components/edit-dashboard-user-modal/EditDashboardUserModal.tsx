import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/Dialog';
import FeatureIcon from '@/assets/images/user/featured-icon.svg';
import {
  useAdminUpdateProfileMutation,
  User,
  UserRole,
  ListStaffDocument,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

import { DashboardUserRole } from '@/lib/constants';
import { useMemo } from 'react';
import DashboardUserForm, {
  DashboardUserFormSchema,
} from '@/components/dashboard-user-form/DashboardUserForm';
import { ApolloError, useApolloClient } from '@apollo/client';

interface EditDashboardUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  user: User;
}

const EditDashboardUserModal = ({
  open,
  onOpenChange,
  onCancel,
  user,
}: EditDashboardUserModalProps) => {
  const initialValues: DashboardUserFormSchema = useMemo(() => {
    return {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email || '',
      role: user.role as unknown as DashboardUserRole,
    };
  }, [user]);

  const client = useApolloClient();
  const { toast } = useToast();
  const [updateDashboardUser, { loading }] = useAdminUpdateProfileMutation();
  const handleCreateDashboardUser = async (data: DashboardUserFormSchema) => {
    try {
      await updateDashboardUser({
        variables: {
          input: {
            userId: user.id,
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            role: data.role as unknown as UserRole,
          },
        },
      });

      client.refetchQueries({
        include: [ListStaffDocument],
      });

      toast({
        variant: 'success',
        title: 'User updated successfully',
      });

      onCancel();
    } catch (error) {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0].message,
        });
        onCancel();
        return;
      }
      console.error('Error updating user:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onOpenAutoFocus={(e) => e.preventDefault()}
        hideClose
        className='sm:max-w-[552px] max-w-[90vw] rounded-lg'
      >
        <DialogHeader className='flex items-start gap-2'>
          <img src={FeatureIcon} alt='logo' width={48} height={48} />
          <DialogTitle className='text-2xl font-semibold'>Edit User Info</DialogTitle>
        </DialogHeader>
        <DashboardUserForm
          onCancel={onCancel}
          onConfirm={handleCreateDashboardUser}
          isEdit
          isLoading={loading}
          initialValues={initialValues}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EditDashboardUserModal;
