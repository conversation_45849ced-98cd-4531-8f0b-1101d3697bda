import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAdminClubPostsQuery, UserRole } from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';

import { TablePagination } from '@/components/ui/table/TablePagination';
import PostTableItem from './PostTableItem';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import { usePostActions } from './hooks/usePostActions';
import { usePostFilters } from './hooks/usePostFilters';
import PostTableFilter from './PostTableFilter';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { DeletePostModal } from './DeletePostModal';
import { DisableClubAccessModal } from '../members/DisableClubAccessModal';
import { format } from 'date-fns';
import { DATE_FORMAT_API } from '@/lib/constants';

interface PostsTabProps {
  clubId: string;
}

const PostsTab = ({ clubId }: PostsTabProps) => {
  const { searchTemp, setSearchTemp } = useSearchQuery('club-posts');
  const { pagination, setPagination } = usePagination();
  const {
    handleRemovePost,
    handleOpenDisableModal,
    handleUnflagPost,
    isRemovingPost,
    isDisableModalOpen,
    setIsDisableModalOpen,
    handleCloseDisableModal,
    handleConfirmDisableClubAccess,
    isDisablingClubAccess,
  } = usePostActions();
  const postFilters = usePostFilters();
  const { userData } = useAuthContext();

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);

  useEffect(() => {
    setPagination({ pageIndex: 1, pageSize: 10 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTemp, postFilters.selectedUserId, postFilters.dateRange, postFilters.showFlaggedOnly]);

  const { data: postsData, loading: isLoadingPosts } = useAdminClubPostsQuery({
    variables: {
      clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp,
        userId: postFilters.selectedUserId || undefined,
        postsDateFrom: postFilters.dateRange?.from
          ? format(postFilters.dateRange.from, DATE_FORMAT_API)
          : undefined,
        postsDateTo: postFilters.dateRange?.to
          ? format(postFilters.dateRange.to, DATE_FORMAT_API)
          : undefined,
        onlyFlagged: postFilters.showFlaggedOnly || undefined,
      },
    },
  });

  const totalPosts = useMemo(() => postsData?.adminClubPosts?.total ?? 0, [postsData]);

  const posts = useMemo(() => {
    if (isLoadingPosts) {
      return Array(10).fill({});
    }
    return postsData?.adminClubPosts?.items ?? [];
  }, [isLoadingPosts, postsData?.adminClubPosts?.items]);

  // Delete modal handlers
  const handleOpenDeleteModal = useCallback((postId: string) => {
    setPostToDelete(postId);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setPostToDelete(null);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (postToDelete) {
      await handleRemovePost(postToDelete);
      handleCloseDeleteModal();
    }
  }, [postToDelete, handleRemovePost, handleCloseDeleteModal]);

  return (
    <div className='w-full flex flex-col py-6 bg-white rounded-lg'>
      {/* Search and Filters */}
      <PostTableFilter
        clubId={clubId}
        search={searchTemp}
        setSearch={setSearchTemp}
        selectedUserId={postFilters.selectedUserId}
        setSelectedUserId={postFilters.setSelectedUserId}
        selectedUserName={postFilters.selectedUserName}
        setSelectedUserName={postFilters.setSelectedUserName}
        dateRange={postFilters.dateRange}
        setDateRange={postFilters.setDateRange}
        showFlaggedOnly={postFilters.showFlaggedOnly}
        setShowFlaggedOnly={postFilters.setShowFlaggedOnly}
      />

      <div className='border rounded-xl overflow-hidden'>
        <TableHeaderCount title='Posts' total={totalPosts} />
        {/* Posts List */}
        <div className='flex flex-col gap-4 px-4'>
          {posts.map((post) => {
            return (
              <PostTableItem
                isLoading={isLoadingPosts}
                key={post.id}
                post={post}
                showActions={userData?.role === UserRole.Admin}
                onRemovePost={handleOpenDeleteModal}
                onDisableClubAccess={handleOpenDisableModal}
                onUnflagPost={handleUnflagPost}
              />
            );
          })}
        </div>

        {totalPosts === 0 && (
          <div className='flex flex-col items-center justify-center h-full'>
            <p className='text-sm py-2'>No Results.</p>
          </div>
        )}

        {/* Pagination */}
        {totalPosts > 0 && (
          <div className='pt-4'>
            <TablePagination
              pageCount={Math.ceil(totalPosts / pagination.pageSize)}
              currentPage={pagination.pageIndex - 1}
              onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
            />
          </div>
        )}
      </div>

      {/* Delete Post Modal */}
      <DeletePostModal
        isOpen={isDeleteModalOpen}
        isDeleting={isRemovingPost}
        onOpenChange={setIsDeleteModalOpen}
        onCancel={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />

      {/* Disable Club Access Modal */}
      <DisableClubAccessModal
        isOpen={isDisableModalOpen}
        isDisabling={isDisablingClubAccess}
        onOpenChange={setIsDisableModalOpen}
        onCancel={handleCloseDisableModal}
        onConfirm={handleConfirmDisableClubAccess}
      />
    </div>
  );
};

export default PostsTab;
