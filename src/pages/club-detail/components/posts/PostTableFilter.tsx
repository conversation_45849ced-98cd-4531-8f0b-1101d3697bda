import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { ChevronDown, ListFilter, X } from 'lucide-react';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import DateRangePicker from '@/components/date-range-picker/DateRangePicker';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/lib/constants';
import { useIsMobile } from '@/hooks/use-mobile';
import { Switch } from '@/components/ui/Switch';
import PostCreatedBySelector from './PostCreatedbySelector';

interface PostTableFilterProps {
  clubId: string;
  search: string;
  setSearch: (search: string) => void;
  selectedUserId: string | null;
  setSelectedUserId: (userId: string | null) => void;
  selectedUserName: string | null;
  setSelectedUserName: (userName: string | null) => void;
  dateRange: DateRange | undefined;
  setDateRange: (date: DateRange | undefined) => void;
  showFlaggedOnly: boolean;
  setShowFlaggedOnly: (showFlagged: boolean) => void;
}

const PostTableFilter = ({
  clubId,
  search,
  setSearch,
  selectedUserId,
  setSelectedUserId,
  selectedUserName,
  setSelectedUserName,
  dateRange,
  setDateRange,
  showFlaggedOnly,
  setShowFlaggedOnly,
}: PostTableFilterProps) => {
  const [isOpenFilters, setIsOpenFilters] = useState(false);
  const [tempSelectedUserId, setTempSelectedUserId] = useState<string | null>(selectedUserId);
  const [tempSelectedUserName, setTempSelectedUserName] = useState<string | null>(selectedUserName);
  const [tempUserSearch, setTempUserSearch] = useState('');
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>(dateRange);
  const [tempShowFlaggedOnly, setTempShowFlaggedOnly] = useState(showFlaggedOnly);
  const [isDateOpen, setIsDateOpen] = useState(false);

  const isMobile = useIsMobile();

  const handleApplyFilters = () => {
    setSelectedUserId(tempSelectedUserId);
    setSelectedUserName(tempSelectedUserName);
    setDateRange(tempDateRange);
    setShowFlaggedOnly(tempShowFlaggedOnly);
    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    setIsOpenFilters(false);
    setTempSelectedUserId(selectedUserId);
    setTempSelectedUserName(selectedUserName);
    setTempUserSearch('');
    setTempDateRange(dateRange);
    setTempShowFlaggedOnly(showFlaggedOnly);
  };

  const onClearAll = () => {
    const defaultUserId = null;
    const defaultUserName = null;
    const defaultDateRange = undefined;
    const defaultShowFlagged = false;

    setSelectedUserId(defaultUserId);
    setSelectedUserName(defaultUserName);
    setDateRange(defaultDateRange);
    setShowFlaggedOnly(defaultShowFlagged);
    setTempSelectedUserId(defaultUserId);
    setTempSelectedUserName(defaultUserName);
    setTempUserSearch('');
    setTempDateRange(defaultDateRange);
    setTempShowFlaggedOnly(defaultShowFlagged);
    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);
    if (!open) {
      setTempSelectedUserId(selectedUserId);
      setTempSelectedUserName(selectedUserName);
      setTempUserSearch('');
      setTempDateRange(dateRange);
      setTempShowFlaggedOnly(showFlaggedOnly);
    }
  };

  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4'>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
        <div className='flex w-full sm:w-auto items-center gap-2'>
          <Popover open={isOpenFilters} onOpenChange={handleFilterOpenChange}>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'
              >
                <ListFilter className='w-5 h-5' />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent align='end' className='sm:w-80 w-[--radix-popover-trigger-width]'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between mb-2'>
                  <label className='text-sm font-medium'>Filter by</label>
                  {(selectedUserId || (dateRange && dateRange.from) || showFlaggedOnly) && (
                    <span className='cursor-pointer text-sm font-medium' onClick={onClearAll}>
                      Clear
                    </span>
                  )}
                </div>

                {/* Date Range Filter */}
                <div className='space-y-2'>
                  <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant='outline'
                        className={cn(
                          'w-full flex justify-between items-center gap-2 font-normal text-[#191E3B] p-3 border border-gray-200 rounded-md text-sm bg-white',
                          {
                            'text-muted-foreground': !tempDateRange?.from,
                          }
                        )}
                      >
                        {tempDateRange?.from ? (
                          tempDateRange.to ? (
                            <>
                              {format(tempDateRange.from, DATE_FORMAT)} -{' '}
                              {format(tempDateRange.to, DATE_FORMAT)}
                            </>
                          ) : (
                            format(tempDateRange.from, DATE_FORMAT)
                          )
                        ) : (
                          <span>Date</span>
                        )}
                        <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className='w-[--radix-popover-trigger-width] items-center sm:w-auto p-0'
                      align='end'
                      sideOffset={isMobile ? -100 : 5}
                    >
                      <DateRangePicker
                        date={tempDateRange}
                        setDate={setTempDateRange}
                        onConfirm={(date) => {
                          setTempDateRange(date);
                          setIsDateOpen(false);
                        }}
                        onCancel={() => {
                          setIsDateOpen(false);
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Created By Filter */}
                <div className='space-y-2'>
                  <PostCreatedBySelector
                    clubId={clubId}
                    selectedUserId={tempSelectedUserId}
                    setSelectedUserId={setTempSelectedUserId}
                    setSelectedUserName={setTempSelectedUserName}
                    tempUserSearch={tempUserSearch}
                    setTempUserSearch={setTempUserSearch}
                  />
                </div>

                {/* Flagged Posts Filter */}
                <div className='flex items-center justify-between py-2'>
                  <div className='space-y-1'>
                    <label className='text-sm font-medium text-gray-700'>Only Flagged</label>
                  </div>
                  <Switch
                    checked={tempShowFlaggedOnly}
                    onCheckedChange={setTempShowFlaggedOnly}
                    aria-label='Show flagged posts only'
                  />
                </div>

                <div className='flex w-full items-center gap-2'>
                  <Button onClick={handleCancelFilters} variant='outline' className='w-full'>
                    Cancel
                  </Button>
                  <Button
                    disabled={false}
                    onClick={handleApplyFilters}
                    variant='default'
                    className='w-full'
                  >
                    Confirm
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Active Filter Badges */}
      <div className='flex flex-col sm:flex-row gap-2'>
        {selectedUserName && (
          <Badge className='text-sm gap-2 w-fit font-medium'>
            {selectedUserName}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setSelectedUserId(null);
                setSelectedUserName(null);
                setTempSelectedUserId(null);
                setTempSelectedUserName(null);
                setTempUserSearch('');
              }}
            />
          </Badge>
        )}
        {dateRange && (dateRange.from || dateRange.to) && (
          <Badge className='text-sm gap-2 w-fit font-medium'>
            {dateRange.from && format(dateRange.from, DATE_FORMAT)}
            {dateRange.to && ` - ${format(dateRange.to, DATE_FORMAT)}`}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setDateRange(undefined);
                setTempDateRange(undefined);
              }}
            />
          </Badge>
        )}
        {showFlaggedOnly && (
          <Badge className='text-sm gap-2 w-fit font-medium'>
            Only Flagged
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setShowFlaggedOnly(false);
                setTempShowFlaggedOnly(false);
              }}
            />
          </Badge>
        )}
      </div>
    </div>
  );
};

export default PostTableFilter;
