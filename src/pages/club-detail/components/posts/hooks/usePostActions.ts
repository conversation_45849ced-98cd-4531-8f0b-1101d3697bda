import {
  AdminClubPostsDocument,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback, useState } from 'react';

export const usePostActions = () => {
  const [removePost, { loading: isRemovingPost }] = useAdminRemoveClubPostByIdMutation();
  const [disableClubAccess, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation();
  const [unflagPost, { loading: isUnflaggingPost }] = useAdminUnflagReportsByPostIdMutation();

  const { toast } = useToast();

  // Disable club access modal state
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false);
  const [userToDisable, setUserToDisable] = useState<string | null>(null);

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleRemovePost = useCallback(
    async (postId: string) => {
      try {
        await removePost({ variables: { postId }, refetchQueries: [AdminClubPostsDocument] });
        toast({
          variant: 'success',
          title: 'Post deleted successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while removing post');
      }
    },
    [handleApiError, removePost, toast]
  );

  // Modal handlers for disable club access
  const handleOpenDisableModal = useCallback((userId: string) => {
    setUserToDisable(userId);
    setIsDisableModalOpen(true);
  }, []);

  const handleCloseDisableModal = useCallback(() => {
    setIsDisableModalOpen(false);
    setUserToDisable(null);
  }, []);

  const handleConfirmDisableClubAccess = useCallback(async () => {
    try {
      if (!userToDisable) {
        toast({
          variant: 'destructive',
          title: 'Error while disabling club access',
          duration: TOAST_DURATION,
        });
        return;
      }
      await disableClubAccess({ variables: { userId: userToDisable } });
      toast({
        variant: 'success',
        title: 'Club access disabled successfully',
        duration: TOAST_DURATION,
      });

      handleCloseDisableModal();
    } catch (error) {
      handleApiError(error, 'Error while disabling club access');
    }
  }, [disableClubAccess, handleApiError, handleCloseDisableModal, toast, userToDisable]);

  const handleUnflagPost = useCallback(
    async (postId: string) => {
      try {
        await unflagPost({ variables: { postId }, refetchQueries: [AdminClubPostsDocument] });
        toast({
          variant: 'success',
          title: 'Post unflagged successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while unflagging post');
      }
    },
    [handleApiError, toast, unflagPost]
  );

  return {
    isDisableModalOpen,
    isRemovingPost,
    isDisablingClubAccess,
    isUnflaggingPost,

    handleRemovePost,
    handleOpenDisableModal,
    handleUnflagPost,
    setIsDisableModalOpen,
    handleCloseDisableModal,
    handleConfirmDisableClubAccess,
  };
};
