import {
  AdminClubEventsDocument,
  useAdminDeleteClubEventMutation,
  useAdminUnflagReportsByEventIdMutation,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback, useState } from 'react';

export const useEventActions = () => {
  const [removeEvent, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [disableClubAccess, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation();
  const [unflagEvent, { loading: isUnflaggingEvent }] = useAdminUnflagReportsByEventIdMutation();

  const { toast } = useToast();

  // Disable club access modal state
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false);
  const [userToDisable, setUserToDisable] = useState<string | null>(null);

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleRemoveEvent = useCallback(
    async (eventId: string) => {
      try {
        await removeEvent({
          variables: { clubEventId: eventId },
          refetchQueries: [AdminClubEventsDocument],
        });
        toast({
          variant: 'success',
          title: 'Event deleted successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while removing event');
      }
    },
    [handleApiError, removeEvent, toast]
  );

  // Modal handlers for disable club access
  const handleOpenDisableModal = useCallback((userId: string) => {
    setUserToDisable(userId);
    setIsDisableModalOpen(true);
  }, []);

  const handleCloseDisableModal = useCallback(() => {
    setIsDisableModalOpen(false);
    setUserToDisable(null);
  }, []);

  const handleConfirmDisableClubAccess = useCallback(async () => {
    if (!userToDisable) {
      toast({
        variant: 'destructive',
        title: 'Error while disabling club access',
        duration: TOAST_DURATION,
      });
      return;
    }
    try {
      await disableClubAccess({ variables: { userId: userToDisable } });
      toast({
        variant: 'success',
        title: 'Club access disabled successfully',
        duration: TOAST_DURATION,
      });

      handleCloseDisableModal();
    } catch (error) {
      handleApiError(error, 'Error while disabling club access');
    }
  }, [disableClubAccess, handleApiError, handleCloseDisableModal, toast, userToDisable]);

  const handleUnflagEvent = useCallback(
    async (eventId: string) => {
      try {
        await unflagEvent({
          variables: { eventId },
          refetchQueries: [AdminClubEventsDocument],
        });
        toast({
          variant: 'success',
          title: 'Event unflagged successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while unflagging event');
      }
    },
    [handleApiError, toast, unflagEvent]
  );

  return {
    isDisableModalOpen,
    isRemovingEvent,
    isDisablingClubAccess,
    isUnflaggingEvent,

    handleRemoveEvent,
    handleOpenDisableModal,
    handleUnflagEvent,
    setIsDisableModalOpen,
    handleCloseDisableModal,
    handleConfirmDisableClubAccess,
  };
};
