import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { Skeleton } from '@/components/ui/Skeleton';
import { AdminClubEvent, ReportStatusEnum } from '@/generated/graphql';
import { Flag, Heart, MoreHorizontal } from 'lucide-react';
import { formatEventDateTime, getDisplayName } from '../../utils';
import { cn } from '@/lib/utils';
import { useMemo, useState } from 'react';

interface EventTableItemProps {
  event: AdminClubEvent;
  isLoading?: boolean;
  showActions: boolean;
  onRemoveEvent: (eventId: string) => void;
  onDisableClubAccess: (userId: string) => void;
  onUnflagEvent: (eventId: string) => void;
}

const EventTableItem = ({
  event,
  isLoading = false,
  showActions,
  onRemoveEvent,
  onDisableClubAccess,
  onUnflagEvent,
}: EventTableItemProps) => {
  const [isShowFullDescription, setIsShowFullDescription] = useState(false);

  const hasOpenReports =
    event.reports && event.reports.some((report) => report.status === ReportStatusEnum.Open);
  const displayName = getDisplayName(
    event.clubProfile?.user?.firstName,
    event.clubProfile?.user?.lastName
  );
  const shortName = displayName
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();

  const eventDescription = useMemo(() => {
    if (isShowFullDescription) {
      return event.description;
    }
    return event.description?.slice(0, 150);
  }, [isShowFullDescription, event.description]);

  const isDeleted = event.deletedAt !== null;

  if (isLoading) {
    return <EventTableItemSkeleton />;
  }

  return (
    <div
      key={event.id}
      className={cn(
        'p-4 sm:p-6 border-b',
        hasOpenReports ? 'bg-[#FCEFED80]' : 'border',
        isDeleted && 'bg-muted'
      )}
    >
      {/* Header with title and flags */}
      <div className='flex items-start justify-between gap-2 sm:gap-4 mb-3 sm:mb-0'>
        <div className='flex flex-col sm:flex-row gap-2 min-w-0 flex-1'>
          <h3 className='text-sm font-medium text-gray-900 whitespace-nowrap'>
            {event.name || 'Event'}
          </h3>

          {hasOpenReports && !isDeleted && (
            <div className='sm:flex w-full hidden min-w-0 overflow-x-auto '>
              <div className='flex items-center gap-1 flex-nowrap'>
                {Array.from(
                  new Set(
                    event.reports
                      ?.filter((report) => report.status === ReportStatusEnum.Open)
                      .map((report) => report.category?.title || 'Other')
                  )
                ).map((reportTitle, index) => (
                  <Badge
                    key={`${reportTitle}-${index}`}
                    className='bg-[#FCEFED] hover:bg-[#FCEFED] gap-1 sm:gap-2 font-medium text-xs sm:text-sm text-destructive-foreground border-none whitespace-nowrap flex-shrink-0 max-w-full'
                  >
                    <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
                    <span className='truncate'>{reportTitle}</span>
                  </Badge>
                ))}
              </div>
            </div>
          )}
          {isDeleted && (
            <Badge className='bg-[#FCEFED] hidden sm:flex gap-1 sm:gap-2 text-xs sm:text-sm text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'>
              <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
              Deleted
            </Badge>
          )}
        </div>
        {showActions && (
          <div className='flex items-center gap-2 flex-shrink-0'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild disabled={isDeleted}>
                <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                  <MoreHorizontal className='w-5 h-5 sm:w-6 sm:h-6' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end' className='min-w-60'>
                {hasOpenReports && (
                  <>
                    <DropdownMenuItem
                      onClick={() => {
                        if (event.clubProfile?.user?.id) {
                          onDisableClubAccess(event.clubProfile.user?.id);
                        }
                      }}
                      disabled={!event.clubProfile?.user?.canUseClubs}
                    >
                      Disable Clubs Access
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUnflagEvent(event.id)}>
                      Unflag Event
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem onClick={() => onRemoveEvent(event.id)}>
                  Delete Event
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Mobile - Reports below title */}
      {hasOpenReports && !isDeleted && (
        <div className='flex w-full sm:hidden mb-3 overflow-x-auto gap-1 sm:gap-2'>
          <div className='flex items-center gap-1 flex-nowrap'>
            {Array.from(
              new Set(
                event.reports
                  ?.filter((report) => report.status === ReportStatusEnum.Open)
                  .map((report) => report.category?.title || 'Other')
              )
            ).map((reportTitle, index) => (
              <Badge
                key={`${reportTitle}-${index}`}
                className='bg-[#FCEFED] hover:bg-[#FCEFED] gap-1 sm:gap-2 font-medium text-xs sm:text-sm text-destructive-foreground border-none whitespace-nowrap flex-shrink-0 max-w-full'
              >
                <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
                <span className='truncate'>{reportTitle}</span>
              </Badge>
            ))}
          </div>
        </div>
      )}
      {isDeleted && (
        <div className='flex w-full sm:hidden mb-3 overflow-x-auto gap-1 sm:gap-2'>
          <Badge className='bg-[#FCEFED] gap-1 sm:gap-2 text-xs sm:text-sm text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'>
            <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
            Deleted
          </Badge>
        </div>
      )}

      {/* Date and time */}
      <p className='text-xs sm:text-[13px] text-muted-foreground mb-1 break-words'>
        {formatEventDateTime(event.startTime, event.endTime)}
      </p>

      {/* Location */}
      {event.location && (
        <p className='text-xs sm:text-[13px] text-muted-foreground mb-3 sm:mb-4 break-words'>
          {event.location}
        </p>
      )}

      {/* Description */}
      <div className='text-gray-700 mb-3 sm:mb-4'>
        <p className='text-sm break-words whitespace-pre-wrap'>
          {eventDescription}
          {event.description && event.description.length > 150 && (
            <span
              className='text-primary cursor-pointer ml-1 hover:underline'
              onClick={() => setIsShowFullDescription(!isShowFullDescription)}
            >
              {isShowFullDescription ? 'See less' : '...See more'}
            </span>
          )}
        </p>
      </div>

      {/* Footer with author and likes */}
      <div className='flex items-center justify-between gap-2'>
        <div className='flex items-center gap-2 min-w-0 flex-1'>
          <Avatar className='w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0'>
            <AvatarImage className='object-cover' src={event.clubProfile?.img?.url ?? undefined} />
            <AvatarFallback className='bg-gradient-to-br from-purple-400 to-yellow-400 text-white text-xs sm:text-sm font-medium'>
              {shortName}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm text-gray-900 font-medium truncate'>{displayName}</span>
        </div>

        <div className='flex items-center gap-1 text-sm text-gray-500 flex-shrink-0'>
          <Heart className='w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0' />
          <span className='text-primary text-xs sm:text-sm'>{event.reactionCount || 0}</span>
        </div>
      </div>
    </div>
  );
};

export default EventTableItem;

const EventTableItemSkeleton = () => {
  return (
    <div className='p-4 sm:p-6 border-b'>
      {/* Header Skeleton */}
      <div className='flex items-start justify-between gap-2 sm:gap-4'>
        <div className='flex flex-col gap-2 min-w-0 flex-1'>
          <Skeleton className='h-5 sm:h-6 w-32 sm:w-48' />
          <div className='flex flex-wrap gap-1 sm:gap-2'>
            <Skeleton className='h-6 w-20 rounded-full' />
            <Skeleton className='h-6 w-16 rounded-full' />
          </div>
        </div>
        <div className='flex items-center gap-2 flex-shrink-0'>
          <Skeleton className='h-8 w-8 rounded' />
        </div>
      </div>

      {/* Date and time Skeleton */}
      <Skeleton className='h-3 sm:h-4 w-48 sm:w-64 mb-1' />

      {/* Location Skeleton */}
      <Skeleton className='h-3 sm:h-4 w-24 sm:w-32 mb-3 sm:mb-4' />

      {/* Description Skeleton */}
      <div className='mb-3 sm:mb-4 space-y-2'>
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-4/5' />
        <Skeleton className='h-4 w-3/5' />
      </div>

      {/* Footer Skeleton */}
      <div className='flex items-center justify-between gap-2'>
        <div className='flex items-center gap-2 min-w-0 flex-1'>
          <Skeleton className='w-8 h-8 sm:w-10 sm:h-10 rounded-full flex-shrink-0' />
          <Skeleton className='h-4 w-20 sm:w-24' />
        </div>

        <div className='flex items-center gap-1 flex-shrink-0'>
          <Skeleton className='h-4 w-4 rounded' />
          <Skeleton className='h-3 sm:h-4 w-4' />
        </div>
      </div>
    </div>
  );
};
