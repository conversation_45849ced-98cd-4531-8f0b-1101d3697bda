import RemoveModal from '@/components/modals/remove-modal/RemoveModal';

interface DisableClubAccessModalProps {
  isOpen: boolean;
  isDisabling: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => Promise<void>;
}

export const DisableClubAccessModal = ({
  isOpen,
  isDisabling,
  onOpenChange,
  onCancel,
  onConfirm,
}: DisableClubAccessModalProps) => {
  return (
    <RemoveModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={onConfirm}
      isLoading={isDisabling}
      title='Disable Club Access'
      confirmText='Disable'
      description="Are you sure you want to disable this user's club access? Once disabled, they will no longer be able to join or post in this club."
    />
  );
};
