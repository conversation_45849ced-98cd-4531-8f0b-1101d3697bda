import { useCallback, useState } from 'react';
import { useToggleUserClubFeatureMutation } from '@/generated/graphql';
import { toast } from '@/hooks/useToast';

export const useMemberActions = () => {
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false);
  const [userToDisable, setUserToDisable] = useState<string | null>(null);

  const [toggleUserClubFeatureMutation, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation({
      refetchQueries: ['AdminClubMembers'],
      onCompleted: () => {
        toast({
          description: 'User club access has been disabled.',
          variant: 'success',
        });
      },
      onError: (error) => {
        toast({
          description: error.message || 'Failed to disable user club access.',
          variant: 'destructive',
        });
      },
    });

  // Modal handlers
  const handleOpenDisableModal = useCallback((userId: string) => {
    setUserToDisable(userId);
    setIsDisableModalOpen(true);
  }, []);

  const handleCloseDisableModal = useCallback(() => {
    setIsDisableModalOpen(false);
    setUserToDisable(null);
  }, []);

  const handleDisableClubAccessMember = useCallback(async () => {
    const targetUserId = userToDisable;

    if (!targetUserId) {
      toast({
        title: 'Error',
        description: 'Missing required information to disable user club access.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await toggleUserClubFeatureMutation({
        variables: {
          userId: targetUserId,
        },
      });
      handleCloseDisableModal();
    } catch (error) {
      // Error is already handled in onError callback
      console.error('Failed to remove member:', error);
    }
  }, [toggleUserClubFeatureMutation, userToDisable, handleCloseDisableModal]);

  return {
    isDisableModalOpen,
    isDisablingClubAccess,
    setIsDisableModalOpen,
    handleOpenDisableModal,
    handleCloseDisableModal,
    handleDisableClubAccessMember,
  };
};
