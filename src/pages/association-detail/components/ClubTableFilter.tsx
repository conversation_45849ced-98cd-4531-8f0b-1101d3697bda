import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { ChevronDown, ListFilter, X } from 'lucide-react';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import DateRangePicker from '@/components/date-range-picker/DateRangePicker';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/lib/constants';
import { useIsMobile } from '@/hooks/use-mobile';
import { ClubCategoryEnum } from '@/generated/graphql';
import { CategorySelector } from '@/components/category-selector/CategorySelector';
import { getClubCategoryLabel } from '@/pages/club-detail/utils';

interface ClubTableFilterProps {
  search: string;
  setSearch: (search: string) => void;
  selectedCategory: ClubCategoryEnum | null;
  setSelectedCategory: (category: ClubCategoryEnum | null) => void;
  dateRange: DateRange | undefined;
  setDateRange: (date: DateRange | undefined) => void;
}

const ClubTableFilter = ({
  search,
  setSearch,
  selectedCategory,
  setSelectedCategory,
  dateRange,
  setDateRange,
}: ClubTableFilterProps) => {
  const [isOpenFilters, setIsOpenFilters] = useState(false);
  const [tempSelectedCategory, setTempSelectedCategory] = useState<ClubCategoryEnum | null>(
    selectedCategory
  );
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>(dateRange);
  const [isDateOpen, setIsDateOpen] = useState(false);

  const isMobile = useIsMobile();

  const hasFilters = selectedCategory || dateRange?.from || dateRange?.to;

  const handleApplyFilters = () => {
    setSelectedCategory(tempSelectedCategory);
    setDateRange(tempDateRange);
    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    setIsOpenFilters(false);
    setTempSelectedCategory(selectedCategory);
    setTempDateRange(dateRange);
  };

  const onClearAll = () => {
    const defaultCategory = null;
    const defaultDateRange = undefined;

    setSelectedCategory(defaultCategory);
    setDateRange(defaultDateRange);
    setTempSelectedCategory(defaultCategory);
    setTempDateRange(defaultDateRange);
    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);
    if (!open) {
      setTempSelectedCategory(selectedCategory);
      setTempDateRange(dateRange);
    }
  };

  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4'>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
        <div className='flex w-full sm:w-auto items-center gap-2'>
          <Popover open={isOpenFilters} onOpenChange={handleFilterOpenChange}>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'
              >
                <ListFilter className='w-5 h-5' />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent align='end' className='sm:w-80 w-[--radix-popover-trigger-width]'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between mb-2'>
                  <label className='text-sm font-medium'>Filter by</label>
                  {hasFilters && (
                    <span className='cursor-pointer text-sm font-medium' onClick={onClearAll}>
                      Clear
                    </span>
                  )}
                </div>

                {/* Category Filter */}
                <div className='space-y-2'>
                  <CategorySelector
                    selectedCategory={tempSelectedCategory}
                    setSelectedCategory={setTempSelectedCategory}
                  />
                </div>

                {/* Last Activity Date Range Filter */}
                <div className='space-y-2'>
                  <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant='outline'
                        className={cn(
                          'w-full flex justify-between items-center gap-2 font-normal text-[#191E3B] p-3 border border-gray-200 rounded-md text-sm bg-white',
                          {
                            'text-muted-foreground': !tempDateRange?.from,
                          }
                        )}
                      >
                        {tempDateRange?.from ? (
                          tempDateRange.to ? (
                            <>
                              {format(tempDateRange.from, DATE_FORMAT)} -{' '}
                              {format(tempDateRange.to, DATE_FORMAT)}
                            </>
                          ) : (
                            format(tempDateRange.from, DATE_FORMAT)
                          )
                        ) : (
                          <span>Last Activity</span>
                        )}
                        <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className='w-[--radix-popover-trigger-width] items-center sm:w-auto p-0'
                      align='end'
                      sideOffset={isMobile ? -100 : 5}
                    >
                      <DateRangePicker
                        date={tempDateRange}
                        setDate={setTempDateRange}
                        onConfirm={(date) => {
                          setTempDateRange(date);
                          setIsDateOpen(false);
                        }}
                        onCancel={() => {
                          setIsDateOpen(false);
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className='flex w-full items-center gap-2'>
                  <Button onClick={handleCancelFilters} variant='outline' className='w-full'>
                    Cancel
                  </Button>
                  <Button
                    disabled={false}
                    onClick={handleApplyFilters}
                    variant='default'
                    className='w-full'
                  >
                    Confirm
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Active Filter Badges */}
      <div className='flex flex-col sm:flex-row gap-2'>
        {selectedCategory && (
          <Badge className='text-sm gap-2 w-fit font-medium'>
            {getClubCategoryLabel(selectedCategory)}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setSelectedCategory(null);
                setTempSelectedCategory(null);
              }}
            />
          </Badge>
        )}
        {dateRange && (dateRange.from || dateRange.to) && (
          <Badge className='text-sm gap-2 w-fit font-medium'>
            {dateRange.from && format(dateRange.from, DATE_FORMAT)}
            {dateRange.to && ` - ${format(dateRange.to, DATE_FORMAT)}`}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setDateRange(undefined);
                setTempDateRange(undefined);
              }}
            />
          </Badge>
        )}
      </div>
    </div>
  );
};

export default ClubTableFilter;
