import { ApolloError, useApolloClient } from '@apollo/client';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/useToast';
import {
  AdminRemoveUserInput,
  ForgotPasswordInput,
  User,
  UsersDocument,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useCallback, useState } from 'react';
import {
  useAdminRemoveUserMutation,
  useAdminSendSignupEmailsMutation,
  useForgotPasswordMutation,
} from '@/generated/graphql';
import { AppRoutePaths } from '@/lib/constants';

export const useUserActions = () => {
  const client = useApolloClient();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isRemoveUserModalOpen, setIsRemoveUserModalOpen] = useState(false);
  const [isResetPasswordModalOpen, setIsResetPasswordModalOpen] = useState(false);

  const [sendSignupEmailsMutation] = useAdminSendSignupEmailsMutation();
  const [removeUserMutation, { loading: isRemovingUser }] = useAdminRemoveUserMutation();
  const [resetPasswordMutation, { loading: isResettingPassword }] = useForgotPasswordMutation();
  const [toggleUserClubFeatureMutation] = useToggleUserClubFeatureMutation();
  const [togglingUserId, setTogglingUserId] = useState<string | null>(null);

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
        });
      }
    },
    [toast]
  );

  // Success toast helper
  const showSuccess = useCallback(
    (message: string) => {
      toast({ variant: 'success', title: message });
    },
    [toast]
  );

  const handleSendSignupEmails = useCallback(
    async (user: User) => {
      try {
        await sendSignupEmailsMutation({
          variables: { input: { userIds: [user.id] } },
          onCompleted: () => {
            if (user.email) {
              return showSuccess(`Signup email sent to ${user.email} successfully`);
            }
            showSuccess(`Synced deeplink for ${user.firstName} ${user.lastName} successfully`);
          },
        });
      } catch (error) {
        handleApiError(
          error,
          user.email ? 'Failed to send signup emails' : 'Failed to sync deeplink'
        );
      }
    },
    [sendSignupEmailsMutation, showSuccess, handleApiError]
  );

  const handleOpenRemoveUserModal = useCallback((user: User) => {
    setSelectedUser(user);
    setIsRemoveUserModalOpen(true);
  }, []);

  const handleOpenResetPasswordModal = useCallback((user: User) => {
    setSelectedUser(user);
    setIsResetPasswordModalOpen(true);
  }, []);

  const handleConfirmRemoveUser = useCallback(
    async (input: AdminRemoveUserInput) => {
      try {
        const res = await removeUserMutation({ variables: { input } });
        client.refetchQueries({ include: [UsersDocument] });
        setIsRemoveUserModalOpen(false);
        showSuccess(res.data?.adminRemoveUser.message ?? 'User removed successfully');
      } catch (error) {
        setIsRemoveUserModalOpen(false);
        handleApiError(error);
      }
    },
    [removeUserMutation, client, handleApiError, showSuccess]
  );

  const handleConfirmResetPassword = useCallback(
    async (input: ForgotPasswordInput) => {
      try {
        await resetPasswordMutation({ variables: { input } });
        setIsResetPasswordModalOpen(false);
        showSuccess(
          `Password reset email sent to ${selectedUser?.email}. They can now set a new password.`
        );
      } catch (error) {
        setSelectedUser(null);
        setIsResetPasswordModalOpen(false);
        handleApiError(error);
      }
    },
    [resetPasswordMutation, selectedUser, handleApiError, showSuccess]
  );

  const handleEditUser = useCallback(
    (userId: string) => {
      navigate(`${AppRoutePaths.USERS}/${userId}`);
    },
    [navigate]
  );

  const handleOpenUserSalesforce = useCallback((salesforceId: string) => {
    window.open(`${process.env.REACT_APP_SALESFORCE_CONTACT_URL}/${salesforceId}/view`, '_blank');
  }, []);

  const handleToggleUserClubAccess = useCallback(
    async (userId: string) => {
      try {
        setTogglingUserId(userId);
        await toggleUserClubFeatureMutation({ variables: { userId } });
        client.refetchQueries({ include: [UsersDocument] });
      } catch (e) {
        handleApiError(e);
      } finally {
        setTogglingUserId(null);
      }
    },
    [client, handleApiError, toggleUserClubFeatureMutation]
  );

  return {
    isRemoveUserModalOpen,
    isResetPasswordModalOpen,
    isRemovingUser,
    isResettingPassword,
    togglingUserId,
    selectedUser,
    setIsRemoveUserModalOpen,
    setIsResetPasswordModalOpen,
    handleSendSignupEmails,
    handleOpenRemoveUserModal,
    handleOpenResetPasswordModal,
    handleConfirmRemoveUser,
    handleConfirmResetPassword,
    handleEditUser,
    handleOpenUserSalesforce,
    handleToggleUserClubAccess,
  };
};
