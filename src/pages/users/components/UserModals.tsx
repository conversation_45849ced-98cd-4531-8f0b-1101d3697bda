import { User } from '@/generated/graphql';
import { useUserActions } from '../hooks/useUserActions';
import RemoveUserModal from '@/components/modals/remove-modal/RemoveModal';
import ResetPasswordModal from '@/pages/edit-user/components/ResetPasswordModal';

interface UserModalProps {
  userActions: ReturnType<typeof useUserActions>;
  selectedUser: User | null;
}

export const UserModals = ({ userActions, selectedUser }: UserModalProps) => {
  const {
    isRemoveUserModalOpen,
    setIsRemoveUserModalOpen,
    isResetPasswordModalOpen,
    setIsResetPasswordModalOpen,
    isRemovingUser,
    isResettingPassword,
    handleConfirmRemoveUser,
    handleConfirmResetPassword,
  } = userActions;

  return (
    <>
      <RemoveUserModal
        isOpen={isRemoveUserModalOpen}
        onOpenChange={setIsRemoveUserModalOpen}
        onCancel={() => setIsRemoveUserModalOpen(false)}
        onConfirm={() => {
          if (selectedUser) {
            handleConfirmRemoveUser({ userId: selectedUser.id });
          }
        }}
        isLoading={isRemovingUser}
        title='Remove User'
        description='Are you sure you want to remove this user? This action cannot be undone.'
      />

      <ResetPasswordModal
        isOpen={isResetPasswordModalOpen}
        onOpenChange={setIsResetPasswordModalOpen}
        onConfirm={() => {
          if (selectedUser && selectedUser.email) {
            handleConfirmResetPassword({ email: selectedUser.email });
          }
        }}
        isLoading={isResettingPassword}
        onCancel={() => setIsResetPasswordModalOpen(false)}
        title={`Reset Password for ${selectedUser?.firstName} ${selectedUser?.lastName}`}
      />
    </>
  );
};
