query AdminAssociations($paginationArgs: PaginationArgs, $filter: AdminAssociationsFilterInput, $orderBy: AdminAssociationsOrderInput) {
  adminAssociations(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      activeClubCount
      clubMemberCount
      id
      memberCount
      name
    }
    total
    page
    limit
  }
}

query AdminAssociationById($adminAssociationByIdId: ID!) {
  adminAssociationById(id: $adminAssociationByIdId) {
    clubMemberCount
    id
    name
    memberCount
    createdAt
    updatedAt
  }
}
