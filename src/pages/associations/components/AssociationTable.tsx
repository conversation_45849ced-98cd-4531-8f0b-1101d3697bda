import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import usePagination from '@/hooks/usePagination';
import useSort from '@/hooks/useSort';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import TableData from '@/components/ui/table/TableData';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { Row } from '@tanstack/react-table';
import { generateAssociationColumns } from './AssociationColumns';
import {
  useAdminAssociationsQuery,
  AdminAssociationsOrderByField,
  OrderDirection,
} from '@/generated/graphql';

import { useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import { useAuthContext } from '@/pages/auth/AuthContext';
import AssociationTableHeaderFilters from './AssociationTableFilter';

const AssociationTable = () => {
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});
  const [columnFilters, setColumnFilters] = useState<any[]>([]);
  const actionCellRef = useRef<HTMLDivElement>(null);

  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('associations');

  const { isLoading: isLoadingAuth } = useAuthContext();

  const navigate = useNavigate();

  // Map frontend column keys to backend enum values
  const mapSortField = (columnId: string): AdminAssociationsOrderByField => {
    const fieldMap: Record<string, AdminAssociationsOrderByField> = {
      name: AdminAssociationsOrderByField.Name,
      updatedAt: AdminAssociationsOrderByField.UpdatedAt,
      memberCount: AdminAssociationsOrderByField.MemberCount,
      clubMemberCount: AdminAssociationsOrderByField.ClubMemberCount,
    };

    return fieldMap[columnId] || AdminAssociationsOrderByField.Name;
  };

  // Reset pagination when search changes
  useEffect(() => {
    if (searchTemp.trim()) {
      setPagination({ pageIndex: 1, pageSize: 10 });
    }
  }, [searchTemp, setPagination]);

  const { data: associations, loading: isLoadingAssociations } = useAdminAssociationsQuery({
    variables: {
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        name: searchTemp.trim() || undefined,
      },
      orderBy:
        sorting.length > 0
          ? {
              field: mapSortField(sorting[0]?.id),
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
  });

  const associationsData = useMemo(() => {
    if (isLoadingAssociations || isLoadingAuth) {
      return Array(10).fill({});
    }
    return associations?.adminAssociations?.items ?? [];
  }, [isLoadingAssociations, isLoadingAuth, associations?.adminAssociations?.items]);

  const totalAssociations = useMemo(
    () => associations?.adminAssociations?.total ?? 0,
    [associations]
  );

  const handleRowClick = useCallback(
    (row: Row<any>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        const actionCell = actionCellRef.current;
        const rect = actionCell.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const actionCellRight = rect.right;

        // Only scroll if the actions don't have enough space to display
        if (actionCellRight > viewportWidth) {
          actionCell.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'start',
          });
        }
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateAssociationColumns({
        isLoading: isLoadingAssociations,
        onNavigate: (associationId: string) =>
          navigate(`${AppRoutePaths.ASSOCIATIONS}/${associationId}`),
      }),
    [isLoadingAssociations, navigate]
  );

  return (
    <div className='w-full flex flex-col sm:p-8 sm:pb-12 flex-1 rounded-lg'>
      <AssociationTableHeaderFilters search={searchTemp} setSearch={setSearchTemp} />
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Associations' total={totalAssociations} />
        <TableData
          columns={columns}
          data={associationsData}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        {totalAssociations > 0 && (
          <TablePagination
            pageCount={Math.ceil(totalAssociations / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        )}
      </div>
    </div>
  );
};

export default AssociationTable;
